"use client";
import React, { useState, useEffect, useMemo } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { useEmployeesForPeriod } from "@/hooks/tanstack-query/useEmployeesForPeriod";
import { useReopenPayslipsMutation } from "@/hooks/tanstack-query/usePayslipActions";

type ReopenPayslipsModalProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  periodId: string;
};

export default function ReopenPayslipsModal({
  open,
  onOpenChange,
  periodId,
}: ReopenPayslipsModalProps) {
  const rawEmployees = useEmployeesForPeriod(periodId);
  const employees = useMemo(() => rawEmployees, [rawEmployees]);
  const { mutate, isPending, error } = useReopenPayslipsMutation(periodId);
  const [selected, setSelected] = useState<string[]>([]);

  useEffect(() => {
    if (open) {
      setSelected(employees.map((e) => e.id));
    }
  }, [open, employees]);
  const toggle = (id: string) =>
    setSelected((prev) =>
      prev.includes(id) ? prev.filter((x) => x !== id) : [...prev, id],
    );
  const selectAll = () => setSelected(employees.map((e) => e.id));
  const selectNone = () => setSelected([]);
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Reopen Payslips</DialogTitle>
        </DialogHeader>
        <div className="mb-2 flex justify-end space-x-2">
          <Button size="sm" variant="outline" onClick={selectAll}>
            Select All
          </Button>
          <Button size="sm" variant="outline" onClick={selectNone}>
            Select None
          </Button>
        </div>
        <div className="max-h-64 space-y-1 overflow-y-auto">
          {employees.map((emp) => (
            <div key={emp.id} className="flex items-center space-x-2">
              <Checkbox
                checked={selected.includes(emp.id)}
                onCheckedChange={() => toggle(emp.id)}
              />
              <span>{emp.name}</span>
            </div>
          ))}
        </div>
        {error && (
          <div className="mb-4 rounded-md bg-red-50 p-3 text-sm text-red-700 dark:bg-red-900/20 dark:text-red-400">
            Error: {error.message}
          </div>
        )}
        <DialogFooter>
          <Button
            variant="ghost"
            onClick={() => onOpenChange(false)}
            disabled={isPending}
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              if (selected.length > 0) {
                mutate(selected, {
                  onSuccess: () => {
                    onOpenChange(false);
                  },
                  onError: (error) => {
                    console.error("Failed to reopen payslips:", error);
                    // Keep modal open to show error
                  },
                });
              }
            }}
            disabled={selected.length === 0 || isPending}
          >
            {isPending ? "Reopening..." : "OK"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
